import { NextRequest, NextResponse } from 'next/server';
import { SUPPORTED_LOCALES, DEFAULT_LOCALE } from '@/lib/constants/languages';

/**
 * Middleware for i18n routing
 * Inspired by Medoo's middleware patterns
 */
export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  console.log('Middleware called for:', pathname);

  // Check if pathname already has a locale
  const pathnameHasLocale = SUPPORTED_LOCALES.some(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  console.log('Pathname has locale:', pathnameHasLocale);

  // If pathname doesn't have a locale, redirect to default locale
  if (!pathnameHasLocale) {
    const locale = getLocaleFromRequest(request);
    console.log('Redirecting to locale:', locale);
    const newUrl = new URL(`/${locale}${pathname}`, request.url);
    return NextResponse.redirect(newUrl);
  }

  return NextResponse.next();
}

/**
 * Get locale from request (cookie, header, etc.)
 */
function getLocaleFromRequest(request: NextRequest): string {
  // Check cookie first
  const cookieLocale = request.cookies.get('NEXT_LOCALE')?.value;
  if (cookieLocale && SUPPORTED_LOCALES.includes(cookieLocale)) {
    return cookieLocale;
  }

  // Check Accept-Language header
  const acceptLanguage = request.headers.get('accept-language');
  if (acceptLanguage) {
    const preferredLocale = acceptLanguage
      .split(',')[0]
      .split('-')[0]
      .toLowerCase();
    
    if (SUPPORTED_LOCALES.includes(preferredLocale)) {
      return preferredLocale;
    }
  }

  return DEFAULT_LOCALE;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
