"use client";

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { Notification, NotificationType, NotificationStatus } from '@zenera/sharing/types';

interface NotificationState {
  // State
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setNotifications: (notifications: Notification[]) => void;
  addNotification: (notification: Notification) => void;
  updateNotification: (notificationId: string, updates: Partial<Notification>) => void;
  removeNotification: (notificationId: string) => void;
  
  // Status management
  markAsRead: (notificationId: string) => void;
  markAsUnread: (notificationId: string) => void;
  markAllAsRead: () => void;
  archiveNotification: (notificationId: string) => void;
  
  // Bulk actions
  markSelectedAsRead: (notificationIds: string[]) => void;
  deleteSelected: (notificationIds: string[]) => void;
  
  // Filtering
  getNotificationsByType: (type: NotificationType) => Notification[];
  getNotificationsByStatus: (status: NotificationStatus) => Notification[];
  getUnreadNotifications: () => Notification[];
  getRecentNotifications: (limit?: number) => Notification[];
  
  // Loading & Error
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // API Actions (placeholder for now)
  fetchNotifications: () => Promise<void>;
  createNotification: (notification: Omit<Notification, '_id' | 'id' | 'created_at' | 'updated_at'>) => Promise<void>;
  
  // Real-time updates
  subscribeToNotifications: () => void;
  unsubscribeFromNotifications: () => void;
}

const calculateUnreadCount = (notifications: Notification[]): number => {
  return notifications.filter(n => n.status === 'unread').length;
};

export const useNotificationStore = create<NotificationState>()(
  persist(
    immer((set, get) => ({
      // Initial state
      notifications: [],
      unreadCount: 0,
      isLoading: false,
      error: null,
      
      // Basic state management
      setNotifications: (notifications) => {
        set((state) => {
          state.notifications = notifications;
          state.unreadCount = calculateUnreadCount(notifications);
        });
      },
      
      addNotification: (notification) => {
        set((state) => {
          state.notifications.unshift(notification); // Add to beginning for newest first
          state.unreadCount = calculateUnreadCount(state.notifications);
        });
      },
      
      updateNotification: (notificationId, updates) => {
        set((state) => {
          const index = state.notifications.findIndex(n => 
            n._id === notificationId || n.id === notificationId
          );
          if (index !== -1) {
            Object.assign(state.notifications[index], updates);
            state.unreadCount = calculateUnreadCount(state.notifications);
          }
        });
      },
      
      removeNotification: (notificationId) => {
        set((state) => {
          state.notifications = state.notifications.filter(n => 
            n._id !== notificationId && n.id !== notificationId
          );
          state.unreadCount = calculateUnreadCount(state.notifications);
        });
      },
      
      // Status management
      markAsRead: (notificationId) => {
        get().updateNotification(notificationId, { status: 'read' });
      },
      
      markAsUnread: (notificationId) => {
        get().updateNotification(notificationId, { status: 'unread' });
      },
      
      markAllAsRead: () => {
        set((state) => {
          state.notifications.forEach(notification => {
            if (notification.status === 'unread') {
              notification.status = 'read';
            }
          });
          state.unreadCount = 0;
        });
      },
      
      archiveNotification: (notificationId) => {
        get().updateNotification(notificationId, { status: 'archived' });
      },
      
      // Bulk actions
      markSelectedAsRead: (notificationIds) => {
        set((state) => {
          notificationIds.forEach(id => {
            const notification = state.notifications.find(n => n._id === id || n.id === id);
            if (notification) {
              notification.status = 'read';
            }
          });
          state.unreadCount = calculateUnreadCount(state.notifications);
        });
      },
      
      deleteSelected: (notificationIds) => {
        set((state) => {
          state.notifications = state.notifications.filter(n => 
            !notificationIds.includes(n._id || '') && !notificationIds.includes(n.id || '')
          );
          state.unreadCount = calculateUnreadCount(state.notifications);
        });
      },
      
      // Filtering
      getNotificationsByType: (type) => {
        const { notifications } = get();
        return notifications.filter(n => n.type === type);
      },
      
      getNotificationsByStatus: (status) => {
        const { notifications } = get();
        return notifications.filter(n => n.status === status);
      },
      
      getUnreadNotifications: () => {
        return get().getNotificationsByStatus('unread');
      },
      
      getRecentNotifications: (limit = 10) => {
        const { notifications } = get();
        return notifications
          .sort((a, b) => new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime())
          .slice(0, limit);
      },
      
      // Loading & Error
      setLoading: (loading) => {
        set((state) => {
          state.isLoading = loading;
        });
      },
      
      setError: (error) => {
        set((state) => {
          state.error = error;
        });
      },
      
      clearError: () => {
        set((state) => {
          state.error = null;
        });
      },
      
      // API Actions (placeholder implementations)
      fetchNotifications: async () => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Replace with actual API call
          const response = await fetch('/api/notifications');
          if (!response.ok) {
            throw new Error('Failed to fetch notifications');
          }
          
          const notifications = await response.json();
          get().setNotifications(notifications);
          
          set((state) => {
            state.isLoading = false;
          });
        } catch (error) {
          set((state) => {
            state.isLoading = false;
            state.error = error instanceof Error ? error.message : 'Failed to fetch notifications';
          });
        }
      },
      
      createNotification: async (notificationData) => {
        try {
          // TODO: Replace with actual API call
          const response = await fetch('/api/notifications', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(notificationData),
          });
          
          if (!response.ok) {
            throw new Error('Failed to create notification');
          }
          
          const notification = await response.json();
          get().addNotification(notification);
        } catch (error) {
          get().setError(error instanceof Error ? error.message : 'Failed to create notification');
          throw error;
        }
      },
      
      // Real-time updates (placeholder implementations)
      subscribeToNotifications: () => {
        // TODO: Implement WebSocket or SSE connection
        console.log('Subscribing to real-time notifications...');
      },
      
      unsubscribeFromNotifications: () => {
        // TODO: Cleanup WebSocket or SSE connection
        console.log('Unsubscribing from real-time notifications...');
      },
    })),
    {
      name: 'zenera-notifications-storage',
      partialize: (state) => ({
        notifications: state.notifications,
        unreadCount: state.unreadCount,
      }),
    }
  )
);
