"use client";

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { User, Role } from '@zenera/sharing/types';

interface AuthState {
  // State
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  refreshAuth: () => Promise<void>;
  setUser: (user: User) => void;
  setToken: (token: string, refreshToken?: string) => void;
  clearError: () => void;
  updateProfile: (updates: Partial<User>) => Promise<void>;
  
  // Permission helpers
  hasRole: (role: Role) => boolean;
  hasPermission: (permission: string) => boolean;
  isAdmin: () => boolean;
  isSeller: () => boolean;
  isCustomer: () => boolean;
}

interface RegisterData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone_number?: string;
  role?: Role;
}

const initialState = {
  user: null,
  token: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

export const useAuthStore = create<AuthState>()(
  persist(
    immer((set, get) => ({
      ...initialState,
      
      // Authentication actions
      login: async (email: string, password: string) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Replace with actual API call
          const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email, password }),
          });
          
          if (!response.ok) {
            throw new Error('Login failed');
          }
          
          const data = await response.json();
          
          set((state) => {
            state.user = data.user;
            state.token = data.token;
            state.refreshToken = data.refreshToken;
            state.isAuthenticated = true;
            state.isLoading = false;
            state.error = null;
          });
        } catch (error) {
          set((state) => {
            state.isLoading = false;
            state.error = error instanceof Error ? error.message : 'Login failed';
          });
          throw error;
        }
      },
      
      register: async (userData: RegisterData) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Replace with actual API call
          const response = await fetch('/api/auth/register', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(userData),
          });
          
          if (!response.ok) {
            throw new Error('Registration failed');
          }
          
          const data = await response.json();
          
          set((state) => {
            state.user = data.user;
            state.token = data.token;
            state.refreshToken = data.refreshToken;
            state.isAuthenticated = true;
            state.isLoading = false;
            state.error = null;
          });
        } catch (error) {
          set((state) => {
            state.isLoading = false;
            state.error = error instanceof Error ? error.message : 'Registration failed';
          });
          throw error;
        }
      },
      
      logout: () => {
        set((state) => {
          Object.assign(state, initialState);
        });
      },
      
      refreshAuth: async () => {
        const { refreshToken } = get();
        if (!refreshToken) {
          get().logout();
          return;
        }
        
        try {
          // TODO: Replace with actual API call
          const response = await fetch('/api/auth/refresh', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ refreshToken }),
          });
          
          if (!response.ok) {
            throw new Error('Token refresh failed');
          }
          
          const data = await response.json();
          
          set((state) => {
            state.token = data.token;
            state.refreshToken = data.refreshToken;
            state.user = data.user;
          });
        } catch (error) {
          get().logout();
          throw error;
        }
      },
      
      setUser: (user: User) => {
        set((state) => {
          state.user = user;
        });
      },
      
      setToken: (token: string, refreshToken?: string) => {
        set((state) => {
          state.token = token;
          if (refreshToken) {
            state.refreshToken = refreshToken;
          }
          state.isAuthenticated = true;
        });
      },
      
      clearError: () => {
        set((state) => {
          state.error = null;
        });
      },
      
      updateProfile: async (updates: Partial<User>) => {
        const { user, token } = get();
        if (!user || !token) {
          throw new Error('Not authenticated');
        }
        
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Replace with actual API call
          const response = await fetch('/api/users/profile', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`,
            },
            body: JSON.stringify(updates),
          });
          
          if (!response.ok) {
            throw new Error('Profile update failed');
          }
          
          const updatedUser = await response.json();
          
          set((state) => {
            state.user = updatedUser;
            state.isLoading = false;
          });
        } catch (error) {
          set((state) => {
            state.isLoading = false;
            state.error = error instanceof Error ? error.message : 'Profile update failed';
          });
          throw error;
        }
      },
      
      // Permission helpers
      hasRole: (role: Role) => {
        const { user } = get();
        return user?.roles?.includes(role) || false;
      },
      
      hasPermission: (permission: string) => {
        const { user } = get();
        if (!user) return false;
        
        // Check admin permissions
        if (user.admin_info?.permissions?.includes(permission)) {
          return true;
        }
        
        // Add more permission logic as needed
        return false;
      },
      
      isAdmin: () => {
        return get().hasRole(Role.ADMIN);
      },
      
      isSeller: () => {
        return get().hasRole(Role.SELLER);
      },
      
      isCustomer: () => {
        return get().hasRole(Role.CUSTOMER);
      },
    })),
    {
      name: 'zenera-auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
