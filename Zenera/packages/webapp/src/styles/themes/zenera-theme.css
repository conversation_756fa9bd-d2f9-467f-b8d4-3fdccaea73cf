/* Zenera Theme System - Flexible and Customizable */
/* Easy to modify colors, fonts, animations, and styles */

/* === BASE THEME === */
.zenera-theme {
  /* Primary Theme Colors */
  --zenera-primary: var(--zenera-primary-600);
  --zenera-primary-hover: var(--zenera-primary-700);
  --zenera-primary-active: var(--zenera-primary-800);
  --zenera-primary-light: var(--zenera-primary-100);
  --zenera-primary-dark: var(--zenera-primary-900);

  /* Secondary Theme Colors */
  --zenera-secondary: var(--zenera-secondary-500);
  --zenera-secondary-hover: var(--zenera-secondary-600);
  --zenera-secondary-active: var(--zenera-secondary-700);
  --zenera-secondary-light: var(--zenera-secondary-100);
  --zenera-secondary-dark: var(--zenera-secondary-800);

  /* Accent Theme Colors */
  --zenera-accent: var(--zenera-accent-500);
  --zenera-accent-hover: var(--zenera-accent-600);
  --zenera-accent-active: var(--zenera-accent-700);
  --zenera-accent-light: var(--zenera-accent-100);
  --zenera-accent-dark: var(--zenera-accent-800);

  /* Background Colors */
  --zenera-bg-primary: #ffffff;
  --zenera-bg-secondary: var(--zenera-secondary-50);
  --zenera-bg-tertiary: var(--zenera-secondary-100);
  --zenera-bg-overlay: rgba(0, 0, 0, 0.5);

  /* Text Colors */
  --zenera-text-primary: var(--zenera-secondary-900);
  --zenera-text-secondary: var(--zenera-secondary-600);
  --zenera-text-tertiary: var(--zenera-secondary-400);
  --zenera-text-inverse: #ffffff;

  /* Border Colors */
  --zenera-border-primary: var(--zenera-secondary-200);
  --zenera-border-secondary: var(--zenera-secondary-300);
  --zenera-border-focus: var(--zenera-primary-500);

  /* Component Specific */
  --zenera-card-bg: #ffffff;
  --zenera-card-border: var(--zenera-border-primary);
  --zenera-card-shadow: var(--zenera-shadow-sm);

  --zenera-button-radius: var(--zenera-radius-md);
  --zenera-input-radius: var(--zenera-radius-md);
  --zenera-card-radius: var(--zenera-radius-lg);

  /* Animation Preferences */
  --zenera-animation-speed: 1;
  --zenera-animation-style: 'metal'; /* metal, wood, water, fire, earth */
}

/* === DARK THEME === */
.zenera-theme.dark {
  /* Background Colors */
  --zenera-bg-primary: var(--zenera-secondary-900);
  --zenera-bg-secondary: var(--zenera-secondary-800);
  --zenera-bg-tertiary: var(--zenera-secondary-700);
  --zenera-bg-overlay: rgba(0, 0, 0, 0.7);

  /* Text Colors */
  --zenera-text-primary: var(--zenera-secondary-50);
  --zenera-text-secondary: var(--zenera-secondary-300);
  --zenera-text-tertiary: var(--zenera-secondary-400);
  --zenera-text-inverse: var(--zenera-secondary-900);

  /* Border Colors */
  --zenera-border-primary: var(--zenera-secondary-700);
  --zenera-border-secondary: var(--zenera-secondary-600);

  /* Component Specific */
  --zenera-card-bg: var(--zenera-secondary-800);
  --zenera-card-border: var(--zenera-border-primary);
  --zenera-card-shadow: var(--zenera-shadow-lg);
}

/* === THEME VARIANTS === */

/* Professional Theme */
.zenera-theme-professional {
  --zenera-primary: #1e40af;
  --zenera-accent: #374151;
  --zenera-button-radius: var(--zenera-radius-sm);
  --zenera-input-radius: var(--zenera-radius-sm);
  --zenera-card-radius: var(--zenera-radius-md);
  --zenera-animation-style: 'earth';
}

/* Creative Theme */
.zenera-theme-creative {
  --zenera-primary: #7c3aed;
  --zenera-accent: #f59e0b;
  --zenera-button-radius: var(--zenera-radius-xl);
  --zenera-input-radius: var(--zenera-radius-lg);
  --zenera-card-radius: var(--zenera-radius-2xl);
  --zenera-animation-style: 'fire';
}

/* Minimal Theme */
.zenera-theme-minimal {
  --zenera-primary: #000000;
  --zenera-accent: #6b7280;
  --zenera-button-radius: var(--zenera-radius-none);
  --zenera-input-radius: var(--zenera-radius-none);
  --zenera-card-radius: var(--zenera-radius-sm);
  --zenera-animation-style: 'metal';
  --zenera-card-shadow: none;
}

/* Luxury Theme */
.zenera-theme-luxury {
  --zenera-primary: #92400e;
  --zenera-accent: #fbbf24;
  --zenera-button-radius: var(--zenera-radius-lg);
  --zenera-input-radius: var(--zenera-radius-lg);
  --zenera-card-radius: var(--zenera-radius-xl);
  --zenera-animation-style: 'crystal';
  --zenera-card-shadow: var(--zenera-shadow-xl);
}

/* === COMPONENT THEMES === */

/* Button Theme */
.zenera-btn {
  background-color: var(--zenera-primary);
  color: var(--zenera-text-inverse);
  border-radius: var(--zenera-button-radius);
  border: none;
  padding: var(--zenera-space-2) var(--zenera-space-4);
  font-weight: var(--zenera-font-medium);
  transition: all var(--zenera-duration-200) var(--zenera-ease-out);
  cursor: pointer;
}

.zenera-btn:hover {
  background-color: var(--zenera-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--zenera-shadow-md);
}

.zenera-btn:active {
  background-color: var(--zenera-primary-active);
  transform: translateY(0);
}

.zenera-btn-secondary {
  background-color: var(--zenera-secondary);
  color: var(--zenera-text-inverse);
}

.zenera-btn-secondary:hover {
  background-color: var(--zenera-secondary-hover);
}

.zenera-btn-outline {
  background-color: transparent;
  color: var(--zenera-primary);
  border: 2px solid var(--zenera-primary);
}

.zenera-btn-outline:hover {
  background-color: var(--zenera-primary);
  color: var(--zenera-text-inverse);
}

/* Card Theme */
.zenera-card {
  background-color: var(--zenera-card-bg);
  border: 1px solid var(--zenera-card-border);
  border-radius: var(--zenera-card-radius);
  box-shadow: var(--zenera-card-shadow);
  padding: var(--zenera-space-6);
  transition: all var(--zenera-duration-300) var(--zenera-ease-out);
}

.zenera-card:hover {
  box-shadow: var(--zenera-shadow-lg);
  transform: translateY(-2px);
}

/* Input Theme */
.zenera-input {
  background-color: var(--zenera-bg-primary);
  border: 2px solid var(--zenera-border-primary);
  border-radius: var(--zenera-input-radius);
  padding: var(--zenera-space-3) var(--zenera-space-4);
  color: var(--zenera-text-primary);
  font-size: var(--zenera-text-base);
  transition: all var(--zenera-duration-200) var(--zenera-ease-out);
}

.zenera-input:focus {
  outline: none;
  border-color: var(--zenera-border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.zenera-input::placeholder {
  color: var(--zenera-text-tertiary);
}

/* === UTILITY CLASSES === */

/* Quick Theme Switchers */
.zenera-theme-switch {
  position: fixed;
  top: var(--zenera-space-4);
  right: var(--zenera-space-4);
  z-index: var(--zenera-z-fixed);
  display: flex;
  gap: var(--zenera-space-2);
}

.zenera-theme-switch button {
  width: 40px;
  height: 40px;
  border-radius: var(--zenera-radius-full);
  border: 2px solid var(--zenera-border-primary);
  cursor: pointer;
  transition: all var(--zenera-duration-200) var(--zenera-ease-out);
}

.zenera-theme-switch button:hover {
  transform: scale(1.1);
  box-shadow: var(--zenera-shadow-md);
}

/* Animation Speed Controls */
.zenera-animation-slow {
  --zenera-animation-speed: 0.5;
}

.zenera-animation-fast {
  --zenera-animation-speed: 2;
}

.zenera-animation-disabled {
  --zenera-animation-speed: 0;
}

/* Apply animation speed to all animations */
* {
  animation-duration: calc(var(--zenera-duration-300) / var(--zenera-animation-speed, 1));
}

/* === RESPONSIVE THEME ADJUSTMENTS === */
@media (max-width: 768px) {
  .zenera-theme {
    --zenera-card-radius: var(--zenera-radius-md);
    --zenera-button-radius: var(--zenera-radius-md);
    --zenera-space-base: var(--zenera-space-4);
  }
}

@media (prefers-reduced-motion: reduce) {
  .zenera-theme {
    --zenera-animation-speed: 0;
  }
  
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
