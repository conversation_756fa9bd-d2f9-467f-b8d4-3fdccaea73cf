/* Zenera Animations - Inspired by Five Elements (<PERSON><PERSON>) */
/* Unique angular and sharp movements with elemental themes */

/* === CORE KEYFRAMES === */

/* Metal Element (Kim) - Sharp, precise, angular movements */
@keyframes zenera-metal-enter {
  0% {
    transform: scale(0.8) rotate(-5deg);
    opacity: 0;
    clip-path: polygon(0 0, 0 0, 0 100%, 0% 100%);
  }
  50% {
    transform: scale(1.05) rotate(2deg);
    opacity: 0.7;
    clip-path: polygon(0 0, 70% 0, 50% 100%, 0% 100%);
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
  }
}

@keyframes zenera-metal-exit {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
  }
  50% {
    transform: scale(1.05) rotate(-2deg);
    opacity: 0.7;
    clip-path: polygon(30% 0, 100% 0, 100% 100%, 50% 100%);
  }
  100% {
    transform: scale(0.8) rotate(5deg);
    opacity: 0;
    clip-path: polygon(100% 0, 100% 0, 100% 100%, 100% 100%);
  }
}

/* Wood Element (Mộc) - Growing, organic movements */
@keyframes zenera-wood-grow {
  0% {
    transform: scaleY(0) scaleX(0.8);
    transform-origin: bottom center;
    opacity: 0;
  }
  30% {
    transform: scaleY(0.6) scaleX(0.9);
    opacity: 0.6;
  }
  60% {
    transform: scaleY(1.1) scaleX(1.05);
    opacity: 0.9;
  }
  100% {
    transform: scaleY(1) scaleX(1);
    opacity: 1;
  }
}

/* Water Element (Thủy) - Flowing, wave-like movements */
@keyframes zenera-water-flow {
  0% {
    transform: translateX(-100%) skewX(10deg);
    opacity: 0;
  }
  30% {
    transform: translateX(-20%) skewX(5deg);
    opacity: 0.7;
  }
  60% {
    transform: translateX(10%) skewX(-2deg);
    opacity: 0.9;
  }
  100% {
    transform: translateX(0) skewX(0deg);
    opacity: 1;
  }
}

/* Fire Element (Hỏa) - Flickering, energetic movements */
@keyframes zenera-fire-flicker {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    filter: brightness(1) hue-rotate(0deg);
  }
  25% {
    transform: scale(1.05) rotate(1deg);
    filter: brightness(1.1) hue-rotate(5deg);
  }
  50% {
    transform: scale(0.98) rotate(-1deg);
    filter: brightness(1.05) hue-rotate(-3deg);
  }
  75% {
    transform: scale(1.02) rotate(0.5deg);
    filter: brightness(1.08) hue-rotate(2deg);
  }
}

/* Earth Element (Thổ) - Stable, grounded movements */
@keyframes zenera-earth-settle {
  0% {
    transform: translateY(-20px) scale(1.1);
    opacity: 0;
  }
  50% {
    transform: translateY(5px) scale(0.98);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-2px) scale(1.01);
    opacity: 0.95;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* === SPECIALIZED ANIMATIONS === */

/* Sharp Angular Slide (Kim-inspired) */
@keyframes zenera-angular-slide-in {
  0% {
    transform: translateX(-100%) skewX(-15deg);
    opacity: 0;
  }
  60% {
    transform: translateX(5%) skewX(5deg);
    opacity: 0.8;
  }
  100% {
    transform: translateX(0) skewX(0deg);
    opacity: 1;
  }
}

/* Blade Cut Effect */
@keyframes zenera-blade-cut {
  0% {
    clip-path: polygon(0 0, 0 0, 0 100%, 0 100%);
    transform: translateX(-10px);
  }
  50% {
    clip-path: polygon(0 0, 60% 0, 40% 100%, 0 100%);
    transform: translateX(2px);
  }
  100% {
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    transform: translateX(0);
  }
}

/* Crystalline Formation */
@keyframes zenera-crystal-form {
  0% {
    transform: scale(0) rotate(45deg);
    opacity: 0;
    clip-path: polygon(50% 50%, 50% 50%, 50% 50%, 50% 50%);
  }
  25% {
    transform: scale(0.3) rotate(35deg);
    opacity: 0.3;
    clip-path: polygon(30% 30%, 70% 30%, 70% 70%, 30% 70%);
  }
  50% {
    transform: scale(0.7) rotate(10deg);
    opacity: 0.7;
    clip-path: polygon(10% 25%, 90% 25%, 75% 90%, 25% 90%);
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  }
}

/* Shatter Effect */
@keyframes zenera-shatter {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
    filter: blur(0px);
  }
  30% {
    transform: scale(1.05) rotate(2deg);
    opacity: 0.9;
    filter: blur(0.5px);
  }
  60% {
    transform: scale(0.95) rotate(-1deg);
    opacity: 0.6;
    filter: blur(1px);
  }
  100% {
    transform: scale(0.8) rotate(5deg);
    opacity: 0;
    filter: blur(2px);
  }
}

/* === UTILITY CLASSES === */

/* Metal Element Animations */
.zenera-animate-metal-enter {
  animation: zenera-metal-enter var(--zenera-duration-300) var(--zenera-ease-sharp) forwards;
}

.zenera-animate-metal-exit {
  animation: zenera-metal-exit var(--zenera-duration-300) var(--zenera-ease-sharp) forwards;
}

/* Wood Element Animations */
.zenera-animate-wood-grow {
  animation: zenera-wood-grow var(--zenera-duration-500) var(--zenera-ease-out) forwards;
}

/* Water Element Animations */
.zenera-animate-water-flow {
  animation: zenera-water-flow var(--zenera-duration-700) var(--zenera-ease-in-out) forwards;
}

/* Fire Element Animations */
.zenera-animate-fire-flicker {
  animation: zenera-fire-flicker var(--zenera-duration-1000) ease-in-out infinite;
}

/* Earth Element Animations */
.zenera-animate-earth-settle {
  animation: zenera-earth-settle var(--zenera-duration-500) var(--zenera-ease-bounce) forwards;
}

/* Specialized Animations */
.zenera-animate-angular-slide {
  animation: zenera-angular-slide-in var(--zenera-duration-300) var(--zenera-ease-sharp) forwards;
}

.zenera-animate-blade-cut {
  animation: zenera-blade-cut var(--zenera-duration-200) var(--zenera-ease-sharp) forwards;
}

.zenera-animate-crystal-form {
  animation: zenera-crystal-form var(--zenera-duration-500) var(--zenera-ease-out) forwards;
}

.zenera-animate-shatter {
  animation: zenera-shatter var(--zenera-duration-300) var(--zenera-ease-in) forwards;
}

/* === HOVER EFFECTS === */
.zenera-hover-metal {
  transition: all var(--zenera-duration-200) var(--zenera-ease-sharp);
}

.zenera-hover-metal:hover {
  transform: translateY(-2px) rotate(1deg);
  box-shadow: var(--zenera-shadow-lg);
  filter: brightness(1.05);
}

.zenera-hover-crystal {
  transition: all var(--zenera-duration-300) var(--zenera-ease-out);
}

.zenera-hover-crystal:hover {
  transform: scale(1.02);
  box-shadow: 
    var(--zenera-shadow-md),
    0 0 20px rgba(59, 130, 246, 0.3);
  filter: brightness(1.1);
}

/* === LOADING ANIMATIONS === */
.zenera-loading-blade {
  position: relative;
  overflow: hidden;
}

.zenera-loading-blade::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: zenera-blade-sweep var(--zenera-duration-1000) ease-in-out infinite;
  transform: skewX(-15deg);
}

@keyframes zenera-blade-sweep {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
