/* Zenera Design System - Design Tokens */
/* Inspired by Medoo patterns with unique Zenera identity */

:root {
  /* === COLORS === */
  /* Primary - Zenera Blue (Professional & Trustworthy) */
  --zenera-primary-50: #eff6ff;
  --zenera-primary-100: #dbeafe;
  --zenera-primary-200: #bfdbfe;
  --zenera-primary-300: #93c5fd;
  --zenera-primary-400: #60a5fa;
  --zenera-primary-500: #3b82f6;
  --zenera-primary-600: #2563eb;
  --zenera-primary-700: #1d4ed8;
  --zenera-primary-800: #1e40af;
  --zenera-primary-900: #1e3a8a;
  --zenera-primary-950: #172554;

  /* Secondary - Zenera Gray (Elegant & Modern) */
  --zenera-secondary-50: #f8fafc;
  --zenera-secondary-100: #f1f5f9;
  --zenera-secondary-200: #e2e8f0;
  --zenera-secondary-300: #cbd5e1;
  --zenera-secondary-400: #94a3b8;
  --zenera-secondary-500: #64748b;
  --zenera-secondary-600: #475569;
  --zenera-secondary-700: #334155;
  --zenera-secondary-800: #1e293b;
  --zenera-secondary-900: #0f172a;
  --zenera-secondary-950: #020617;

  /* Accent - Zenera Gold (Premium & Luxury) */
  --zenera-accent-50: #fffbeb;
  --zenera-accent-100: #fef3c7;
  --zenera-accent-200: #fde68a;
  --zenera-accent-300: #fcd34d;
  --zenera-accent-400: #fbbf24;
  --zenera-accent-500: #f59e0b;
  --zenera-accent-600: #d97706;
  --zenera-accent-700: #b45309;
  --zenera-accent-800: #92400e;
  --zenera-accent-900: #78350f;
  --zenera-accent-950: #451a03;

  /* Semantic Colors */
  --zenera-success-50: #f0fdf4;
  --zenera-success-500: #22c55e;
  --zenera-success-600: #16a34a;
  --zenera-success-700: #15803d;

  --zenera-warning-50: #fffbeb;
  --zenera-warning-500: #f59e0b;
  --zenera-warning-600: #d97706;
  --zenera-warning-700: #b45309;

  --zenera-error-50: #fef2f2;
  --zenera-error-500: #ef4444;
  --zenera-error-600: #dc2626;
  --zenera-error-700: #b91c1c;

  --zenera-info-50: #eff6ff;
  --zenera-info-500: #3b82f6;
  --zenera-info-600: #2563eb;
  --zenera-info-700: #1d4ed8;

  /* === TYPOGRAPHY === */
  /* Font Families */
  --zenera-font-sans: 'Inter', system-ui, -apple-system, sans-serif;
  --zenera-font-display: 'Poppins', 'Inter', system-ui, sans-serif;
  --zenera-font-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;

  /* Font Sizes */
  --zenera-text-xs: 0.75rem;     /* 12px */
  --zenera-text-sm: 0.875rem;    /* 14px */
  --zenera-text-base: 1rem;      /* 16px */
  --zenera-text-lg: 1.125rem;    /* 18px */
  --zenera-text-xl: 1.25rem;     /* 20px */
  --zenera-text-2xl: 1.5rem;     /* 24px */
  --zenera-text-3xl: 1.875rem;   /* 30px */
  --zenera-text-4xl: 2.25rem;    /* 36px */
  --zenera-text-5xl: 3rem;       /* 48px */
  --zenera-text-6xl: 3.75rem;    /* 60px */

  /* Font Weights */
  --zenera-font-light: 300;
  --zenera-font-normal: 400;
  --zenera-font-medium: 500;
  --zenera-font-semibold: 600;
  --zenera-font-bold: 700;
  --zenera-font-extrabold: 800;

  /* Line Heights */
  --zenera-leading-tight: 1.25;
  --zenera-leading-snug: 1.375;
  --zenera-leading-normal: 1.5;
  --zenera-leading-relaxed: 1.625;
  --zenera-leading-loose: 2;

  /* === SPACING === */
  --zenera-space-0: 0;
  --zenera-space-px: 1px;
  --zenera-space-0-5: 0.125rem;  /* 2px */
  --zenera-space-1: 0.25rem;     /* 4px */
  --zenera-space-1-5: 0.375rem;  /* 6px */
  --zenera-space-2: 0.5rem;      /* 8px */
  --zenera-space-2-5: 0.625rem;  /* 10px */
  --zenera-space-3: 0.75rem;     /* 12px */
  --zenera-space-3-5: 0.875rem;  /* 14px */
  --zenera-space-4: 1rem;        /* 16px */
  --zenera-space-5: 1.25rem;     /* 20px */
  --zenera-space-6: 1.5rem;      /* 24px */
  --zenera-space-7: 1.75rem;     /* 28px */
  --zenera-space-8: 2rem;        /* 32px */
  --zenera-space-9: 2.25rem;     /* 36px */
  --zenera-space-10: 2.5rem;     /* 40px */
  --zenera-space-12: 3rem;       /* 48px */
  --zenera-space-14: 3.5rem;     /* 56px */
  --zenera-space-16: 4rem;       /* 64px */
  --zenera-space-20: 5rem;       /* 80px */
  --zenera-space-24: 6rem;       /* 96px */
  --zenera-space-32: 8rem;       /* 128px */

  /* === BORDER RADIUS === */
  --zenera-radius-none: 0;
  --zenera-radius-sm: 0.125rem;   /* 2px */
  --zenera-radius-base: 0.25rem;  /* 4px */
  --zenera-radius-md: 0.375rem;   /* 6px */
  --zenera-radius-lg: 0.5rem;     /* 8px */
  --zenera-radius-xl: 0.75rem;    /* 12px */
  --zenera-radius-2xl: 1rem;      /* 16px */
  --zenera-radius-3xl: 1.5rem;    /* 24px */
  --zenera-radius-full: 9999px;

  /* === SHADOWS === */
  --zenera-shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --zenera-shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --zenera-shadow-base: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --zenera-shadow-md: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --zenera-shadow-lg: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --zenera-shadow-xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --zenera-shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --zenera-shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);

  /* === Z-INDEX === */
  --zenera-z-auto: auto;
  --zenera-z-0: 0;
  --zenera-z-10: 10;
  --zenera-z-20: 20;
  --zenera-z-30: 30;
  --zenera-z-40: 40;
  --zenera-z-50: 50;
  --zenera-z-dropdown: 1000;
  --zenera-z-sticky: 1020;
  --zenera-z-fixed: 1030;
  --zenera-z-modal-backdrop: 1040;
  --zenera-z-modal: 1050;
  --zenera-z-popover: 1060;
  --zenera-z-tooltip: 1070;
  --zenera-z-toast: 1080;

  /* === ANIMATION DURATIONS === */
  --zenera-duration-75: 75ms;
  --zenera-duration-100: 100ms;
  --zenera-duration-150: 150ms;
  --zenera-duration-200: 200ms;
  --zenera-duration-300: 300ms;
  --zenera-duration-500: 500ms;
  --zenera-duration-700: 700ms;
  --zenera-duration-1000: 1000ms;

  /* === ANIMATION EASING === */
  --zenera-ease-linear: linear;
  --zenera-ease-in: cubic-bezier(0.4, 0, 1, 1);
  --zenera-ease-out: cubic-bezier(0, 0, 0.2, 1);
  --zenera-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --zenera-ease-sharp: cubic-bezier(0.4, 0, 0.6, 1);
  --zenera-ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* === BREAKPOINTS === */
  --zenera-screen-sm: 640px;
  --zenera-screen-md: 768px;
  --zenera-screen-lg: 1024px;
  --zenera-screen-xl: 1280px;
  --zenera-screen-2xl: 1536px;
}

/* Dark Theme */
.dark {
  /* Override colors for dark theme */
  --zenera-primary-50: #172554;
  --zenera-primary-500: #60a5fa;
  --zenera-primary-600: #3b82f6;
  
  --zenera-secondary-50: #020617;
  --zenera-secondary-100: #0f172a;
  --zenera-secondary-200: #1e293b;
  --zenera-secondary-500: #94a3b8;
  --zenera-secondary-900: #f8fafc;
}
