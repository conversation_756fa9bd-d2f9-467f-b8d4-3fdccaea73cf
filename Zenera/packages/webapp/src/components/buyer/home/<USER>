"use client";

import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface BuyerHomePageProps {
  locale: string;
}

export function BuyerHomePage({ locale }: BuyerHomePageProps) {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">Zenera</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline">{t('common.login', 'Login')}</Button>
              <Button>{t('common.register', 'Register')}</Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-gray-900 mb-6">
            {t('home.hero.title', 'Welcome to Zenera E-commerce Platform')}
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            {t('home.hero.description', 'Discover amazing products from trusted sellers around the world. Shop with confidence and enjoy fast, secure delivery.')}
          </p>
          <div className="flex justify-center space-x-4">
            <Button size="lg" className="px-8">
              {t('home.hero.shop_now', 'Shop Now')}
            </Button>
            <Button variant="outline" size="lg" className="px-8">
              {t('home.hero.learn_more', 'Learn More')}
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              {t('home.features.title', 'Why Choose Zenera?')}
            </h3>
            <p className="text-lg text-gray-600">
              {t('home.features.subtitle', 'Experience the best in online shopping')}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardHeader>
                <CardTitle>{t('home.features.secure.title', 'Secure Shopping')}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  {t('home.features.secure.description', 'Your data and payments are protected with industry-leading security measures.')}
                </CardDescription>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardHeader>
                <CardTitle>{t('home.features.fast.title', 'Fast Delivery')}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  {t('home.features.fast.description', 'Get your orders delivered quickly with our reliable shipping partners.')}
                </CardDescription>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardHeader>
                <CardTitle>{t('home.features.support.title', '24/7 Support')}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  {t('home.features.support.description', 'Our customer support team is always ready to help you with any questions.')}
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Language Test Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            {t('home.language_test.title', 'Language Test')}
          </h3>
          <p className="text-gray-600 mb-6">
            {t('home.language_test.current_locale', `Current locale: ${locale}`)}
          </p>
          <div className="flex justify-center space-x-4">
            <Button 
              variant="outline" 
              onClick={() => window.location.href = '/en'}
            >
              English
            </Button>
            <Button 
              variant="outline"
              onClick={() => window.location.href = '/vi'}
            >
              Tiếng Việt
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
